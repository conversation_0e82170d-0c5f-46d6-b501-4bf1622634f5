# Pantheon Backend

Pantheon is a service that provides snapshot-based command execution capabilities. It uses Docker to create snapshots and run commands, with plans to extend to Kubernetes and JuiceFS in the future.

## Features

- Executes commands on snapshots and creates new snapshots
- Manages snapshots in a tree structure
- Supports creating seed snapshots from Docker images
- Provides RESTful APIs for all operations

## Requirements

- Go 1.19+
- Docker
- MySQL 8.0+

## Installation

```bash
go get github.com/breezewish/pantheon-backend
```

## Running

```bash
# Using default settings
go run cmd/pantheon/main.go

# With custom settings
go run cmd/pantheon/main.go --port 8080 --host 0.0.0.0 --db "user:pass@tcp(localhost:3306)/pantheon" --data-dir "./data"
```

## API Documentation

### Seed Snap Operations

#### Create a seed snap

```
POST /api/v1/seed_snap/import
```

Request body:
```json
{
  "id": "my-base",
  "envs": {
    "KEY": "VALUE"
  },
  "dockerImage": "alpine:latest"
}
```

### Snap Operations

#### Execute a command on a snap

```
POST /api/v1/forest/{ForestID}/snap/{SnapID}/exec
```

Request body:
```json
{
  "command": ["echo", "Hello, World!"],
  "workingDir": "/",
  "newSnapName": "hello-snap",
  "envs": {
    "KEY": "VALUE"
  }
}
```

Response:
```json
{
  "newSnapID": "my-base/a1b2c3d4"
}
```

#### Get a snap

```
GET /api/v1/forest/{ForestID}/snap/{SnapID}
```

Response:
```json
{
  "id": "my-base/a1b2c3d4",
  "name": "hello-snap",
  "sourceSnapID": "my-base",
  "baseSnapID": "my-base",
  "startAt": "Wed, 17 Aug 2023 10:00:00 GMT",
  "finishAt": "Wed, 17 Aug 2023 10:00:05 GMT",
  "isRunning": false,
  "isShaked": false
}
```

#### Get a snap's output

```
GET /api/v1/forest/{ForestID}/snap/{SnapID}/artifacts/output
```

Response: Raw command output

#### Get a snap tree

```
GET /api/v1/forest/{ForestID}/snap/{SnapID}/tree
```

Optional query parameters:
- maxDepth: Maximum depth of the tree to return

Response: Array of snap objects

#### Get leaf snaps

```
GET /api/v1/forest/{ForestID}/snap/{SnapID}/leaves
```

Response: Array of snap objects

### Forest Operations

#### Burn a forest

```
POST /api/v1/forest/{ForestID}/burn
```

## Testing

```bash
go test ./...
```

## License

MIT