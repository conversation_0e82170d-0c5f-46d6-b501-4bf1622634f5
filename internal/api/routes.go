package api

import (
	"github.com/gin-gonic/gin"
)

// SetupRoutes sets up the API routes.
func SetupRoutes(r *gin.Engine, handler *Handler) {
	r.GET("/health", func(c *gin.Context) {
		c.<PERSON>SO<PERSON>(200, gin.H{"status": "ok"})
	})

	api := r.Group("/api/v1")
	forest := api.Group("/forest/:forestID")
	{
		snap := forest.Group("/snap/:snapID")
		{
			snap.POST("/exec", handler.ExecSnap)
			snap.GET("", handler.GetSnap)
			snap.GET("/artifacts/output", handler.GetSnapOutput)
			// snap.GET("/tree", handler.GetSnapTree)
			// snap.GET("/leaves", handler.GetSnapLeaves)
		}

		forest.POST("/burn", handler.BurnForest)
	}

	seedSnap := api.Group("/seed_snap")
	{
		seedSnap.POST("/import", handler.ImportSeedSnap)
	}
}
