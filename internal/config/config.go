package config

import (
	"fmt"
	"os"
	"path/filepath"
)

// Config holds the application configuration.
type Config struct {
	HTTP     HTTPConfig
	Database DatabaseConfig
	Storage  StorageConfig
}

// HTTPConfig holds the HTTP server configuration.
type HTTPConfig struct {
	Port int
	Host string
}

// DatabaseConfig holds the database configuration.
type DatabaseConfig struct {
	DSN string
}

// StorageConfig holds the storage configuration.
type StorageConfig struct {
	DataDir string
}

// NewConfig creates a new Config instance with default values.
func NewConfig() *Config {
	return &Config{
		HTTP: HTTPConfig{
			Port: 8080,
			Host: "0.0.0.0",
		},
		Database: DatabaseConfig{
			DSN: "root:root@tcp(localhost:3306)/pantheon?charset=utf8mb4&parseTime=True&loc=Local",
		},
		Storage: StorageConfig{
			DataDir: "./data",
		},
	}
}

// EnsureDataDirs ensures that all required data directories exist.
func (c *Config) EnsureDataDirs() error {
	if err := os.MkdirAll(c.Storage.DataDir, 0755); err != nil {
		return fmt.Errorf("failed to create data directory %s: %w", c.Storage.DataDir, err)
	}
	return nil
}

// GetOutputPath returns the path for storing output artifacts for a given forest and snap ID.
func (c *Config) GetOutputPath(forestID, snapID string) string {
	return filepath.Join(c.Storage.DataDir, forestID, snapID, "output.log")
}