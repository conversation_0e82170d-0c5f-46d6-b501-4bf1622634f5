package app

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/breezewish/pantheon-backend/internal/api"
	"github.com/breezewish/pantheon-backend/internal/config"
	"github.com/breezewish/pantheon-backend/internal/model"
	"github.com/breezewish/pantheon-backend/internal/provider/local"
	"github.com/breezewish/pantheon-backend/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// NewGinEngine creates a new Gin engine.
func NewGinEngine() *gin.Engine {
	return gin.Default()
}

// NewGormDB creates a new GORM DB connection.
func NewGormDB(config *config.Config) (*gorm.DB, error) {
	db, err := gorm.Open(mysql.Open(config.Database.DSN), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Auto migrate the schema
	if err := db.AutoMigrate(&model.Snap{}); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	return db, nil
}

// RegisterHooks registers lifecycle hooks.
func RegisterHooks(lc fx.Lifecycle, r *gin.Engine, handler *api.Handler, config *config.Config) error {
	// Set up routes
	api.SetupRoutes(r, handler)

	// Start HTTP server
	server := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", config.HTTP.Host, config.HTTP.Port),
		Handler: r,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
					fmt.Printf("Error starting server: %s\n", err)
				}
			}()
			fmt.Printf("Server started at %s:%d\n", config.HTTP.Host, config.HTTP.Port)
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return server.Shutdown(ctx)
		},
	})

	return nil
}

// DefaultShutdownTimeout is the default timeout for graceful shutdown.
const DefaultShutdownTimeout = 15 * time.Second

// NewFxApp creates a new Fx application.
func NewFxApp(cfg *config.Config) *fx.App {
	return fx.New(
		fx.Supply(cfg),
		fx.Provide(
			NewGinEngine,
			NewGormDB,
			local.NewLocalProvider,
			service.NewSnapService,
			api.NewHandler,
		),
		fx.Invoke(RegisterHooks),
	)
}
