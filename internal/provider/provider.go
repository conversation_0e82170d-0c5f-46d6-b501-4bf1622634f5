package provider

import (
	"go.uber.org/atomic"
)

// ProviderOutput is the result of an execution.
type AsyncExecResult struct {
	StdoutToken  string
	StderrToken  string
	Finish<PERSON>h     chan error     // Channel to signal when execution is finished.
	ExitCode     *atomic.Int32  // Exit code of the executed command, only available after FinishCh returns nil
	NewSnapToken *atomic.String // New ID after execution, only available after FinishCh returns nil
}

// AsyncExecOpts holds the options for asynchronous execution.
type AsyncExecOpts struct {
	SrcSnapToken string
	Command      []string
	WorkDir      string
	Envs         map[string]string
	SkipCommit   bool // If true, the container will not be committed after execution
}

// Provider is the interface for snapshot and execution engines.
type Provider interface {
	// Import creates a base snapshot from a given image and returns its token.
	// Token is a provider-specific ID to represent the snapshot.
	Import(baseImage string) (string, error)

	// AsyncExec runs a command on a source snapshot and creates a new one.
	AsyncExec(opts AsyncExecOpts) (*AsyncExecResult, error)

	// GetStdout retrieves the stdout log for a given execution.
	GetStdout(stdOutToken string) (string, error)

	// GetStderr retrieves the stderr log for a given execution.
	GetStderr(stdErrToken string) (string, error)

	// Cleanup removes a snapshot managed by the provider.
	Cleanup(token string) error
}

// ProviderHelper is only for easier testing purpose.
type ProviderHelper struct {
	Provider
}

type ExecResult struct {
	Stdout       string
	Stderr       string
	Err          error
	ExitCode     int
	NewSnapToken string
}

func (ph ProviderHelper) ExecReadOnly(opts AsyncExecOpts) (*ExecResult, error) {
	opts.SkipCommit = true
	return ph.Exec(opts)
}

func (ph ProviderHelper) Exec(opts AsyncExecOpts) (*ExecResult, error) {
	result, err := ph.Provider.AsyncExec(opts)
	if err != nil {
		return nil, err
	}
	err = <-result.FinishCh
	if err != nil {
		return nil, err
	}
	stdout, _ := ph.Provider.GetStdout(result.StdoutToken)
	stderr, _ := ph.Provider.GetStderr(result.StderrToken)
	return &ExecResult{
		Stdout:       stdout,
		Stderr:       stderr,
		Err:          err,
		ExitCode:     int(result.ExitCode.Load()),
		NewSnapToken: result.NewSnapToken.Load(),
	}, nil
}
