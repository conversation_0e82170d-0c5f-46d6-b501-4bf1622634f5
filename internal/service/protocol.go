package service

import (
	"github.com/breezewish/pantheon-backend/internal/model"
	"github.com/guregu/null/v5"
)

type SnapObject struct {
	ID         string
	ForestID   string
	Name       string
	BaseSnapID string
	Command    []string
	WorkDir    string
	Envs       map[string]string
	StartedAt  null.Int64
	FinishedAt null.Int64
	ExitCode   null.Int32
	Err        null.String
}

func newSnapObject(snap *model.Snap) SnapObject {
	return SnapObject{
		ID:         snap.ID,
		ForestID:   snap.ForestID,
		Name:       snap.Name,
		BaseSnapID: snap.BaseSnapID,
		Command:    snap.Command,
		WorkDir:    snap.WorkDir,
		Envs:       snap.Envs,
		StartedAt:  snap.StartedAt,
		FinishedAt: snap.FinishedAt,
		ExitCode:   snap.ExitCode,
		Err:        snap.Err,
	}
}

// ExecResponse represents the output of an exec operation.
type ExecResponse struct {
	Snap SnapObject
}

// ExecRequest represents the input for an exec operation.
type ExecRequest struct {
	Command     []string
	WorkingDir  string
	NewSnapName string
	Envs        map[string]string
}

// ImportSeedResponse represents the output of an import seed operation.
type ImportSeedResponse struct {
	Snap SnapObject
}

// ImportSeedRequest represents the input for an import seed operation.
type ImportSeedRequest struct {
	ID          string
	Envs        map[string]string
	DockerImage string
}
